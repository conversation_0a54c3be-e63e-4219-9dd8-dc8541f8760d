import os
import sys
import datetime
import threading
import unicodedata
from typing import Any, Dict, List, Optional, Tuple
import tkinter as tk
from tkinter import filedialog, messagebox, scrolledtext

try:
    import pyodbc
except ImportError:
    messagebox.showerror("Erro de Dependência", "A biblioteca 'pyodbc' não está instalada.\n\nExecute no seu terminal:\npip install pyodbc")
    sys.exit(1)


def find_access_driver() -> Optional[str]:
    """Tenta encontrar um driver ODBC do Access instalado no Windows."""
    candidates = [
        "Microsoft Access Driver (*.mdb, *.accdb)",
        "Microsoft Access Driver (*.mdb)",
        "Microsoft Access Driver (*.accdb)",
    ]
    available = [d for d in pyodbc.drivers()]
    for cand in candidates:
        if cand in available:
            return cand
    return None


def connect_mdb(db_path: str) -> pyodbc.Connection:
    driver = find_access_driver()
    if not driver:
        print("Erro: Nenhum driver ODBC do Microsoft Access foi encontrado no sistema.", file=sys.stderr)
        print("Instale o 'Microsoft Access Database Engine' (32 ou 64 bits conforme seu Python).", file=sys.stderr)
        sys.exit(2)
    conn_str = f"DRIVER={{{driver}}};DBQ={db_path};READONLY=TRUE;"
    try:
        return pyodbc.connect(conn_str)
    except pyodbc.Error as e:
        print(f"Erro ao conectar ao MDB: {e}", file=sys.stderr)
        sys.exit(3)


def normalize_name(name: str) -> str:
    """Normaliza um nome para ser um identificador SQL seguro."""
    if not name:
        return ""
    # NFD normaliza caracteres acentuados em caractere base + acento
    nfkd_form = unicodedata.normalize('NFKD', name)
    # Remove os acentos (combinadores)
    sem_acentos = "".join([c for c in nfkd_form if not unicodedata.combining(c)])
    # Substitui espaços e converte para minúsculas
    return sem_acentos.replace(' ', '_').lower()


def quote_ident(name: str) -> str:
    """Normaliza o nome do identificador."""
    return normalize_name(name)


def is_quantidade_field(name: str) -> bool:
    """Retorna True se o nome do campo contiver 'quantidade' (case-insensitive, normalizado)."""
    if not name:
        return False
    n = normalize_name(name)
    return "quantidade" in n


def is_hora_field(name: str) -> bool:
    """Retorna True se o nome do campo contiver 'hora' ou 'horario' (case-insensitive, normalizado)."""
    if not name:
        return False
    n = normalize_name(name)
    return "hora" in n or "horario" in n


# Mapear tipos do Access/ODBC para PostgreSQL
ACCESS_TO_PG_TYPE: Dict[str, str] = {
    # Texto
    "VARCHAR": "varchar(100)",
    "LONGCHAR": "text",
    "TEXT": "text",
    "MEMO": "text",
    # Inteiros
    "BYTE": "numeric(10,2)",
    "SHORT": "numeric(10,2)",
    "INTEGER": "integer",
    "LONG": "numeric(10,2)",
    "COUNTER": "numeric(10,2)",  # autonumber; não marcamos como serial para manter portabilidade
    # Numéricos
    "NUMERIC": "numeric(10,2)",
    "DECIMAL": "numeric(10,2)",
    "CURRENCY": "numeric(10,2)",
    "DOUBLE": "numeric(10,2)",
    "SINGLE": "real",
    # Booleano
    "YESNO": "boolean",
    "BIT": "boolean",
    # Datas
    "DATETIME": "date",
    "DATE": "date",
    "TIME": "time",
    # Binário
    "BINARY": "bytea",
    "LONGBINARY": "bytea",
    "OLEOBJECT": "bytea",
    # GUID
    "GUID": "uuid",
}


def odbc_type_to_pg(type_name: Optional[str], column_size: Optional[int], decimal_digits: Optional[int]) -> str:
    t = (type_name or "").upper()
    base = ACCESS_TO_PG_TYPE.get(t)
    if base is None:
        # fallback genérico por família de tipo
        if t in {"CHAR", "NCHAR", "NVARCHAR"}:
            base = "varchar(100)"
        elif t in {"FLOAT", "DOUBLE", "REAL"}:
            base = "double precision" if t != "REAL" else "real"
        elif t in {"TINYINT", "SMALLINT"}:
            base = "numeric(10)" 
        elif t in {"INT", "INTEGER", "LONG"}:
            base = "integer" 
        elif t in {"BIGINT"}:
            base = "bigint"
        elif t in {"DECIMAL", "NUMERIC"}:
            base = "numeric"
        elif t in {"BIT", "BOOLEAN", "YESNO"}:
            base = "boolean"
        elif t in {"DATETIME", "TIMESTAMP", "DATE", "TIME"}:
            base = "datetime"
        else:
            base = "varchar(100)"  # último recurso
    return base


def get_tables(cursor: pyodbc.Cursor) -> List[str]:
    tables: List[str] = []
    for row in cursor.tables(tableType='TABLE'):
        name = row.table_name
        if not name:
            continue
        if name.startswith("MSys"):
            continue  # ignora tabelas de sistema do Access
        tables.append(name)
    return tables


def get_columns(cursor: pyodbc.Cursor, table: str) -> List[Dict[str, Any]]:
    cols: List[Dict[str, Any]] = []
    for col in cursor.columns(table=table):
        cols.append({
            "name": col.column_name,
            "type_name": getattr(col, 'type_name', None),
            "data_type": getattr(col, 'data_type', None),  # código ODBC (não usamos diretamente)
            "column_size": getattr(col, 'column_size', None),
            "decimal_digits": getattr(col, 'decimal_digits', None),
            "nullable": bool(getattr(col, 'nullable', True)),
            "remarks": getattr(col, 'remarks', None),
            "ordinal_position": getattr(col, 'ordinal_position', None),
        })
    # Ordena pela posição ordinal se disponível
    if cols and any(c.get('ordinal_position') is not None for c in cols):
        cols.sort(key=lambda c: (c.get('ordinal_position') is None, c.get('ordinal_position', 0)))
    return cols


def get_primary_keys(cursor: pyodbc.Cursor, table: str) -> List[str]:
    pks: List[str] = []
    try:
        for pk in cursor.primaryKeys(table=table):
            if pk.column_name:
                pks.append(pk.column_name)
    except Exception:
        pass
    return pks


def format_value(value: Any) -> str:
    if value is None:
        return "NULL"
    if isinstance(value, bool):
        return "TRUE" if value else "FALSE"
    if isinstance(value, (int, float)):
        # Cobre numeric simples; cuidado com NaN/inf
        if isinstance(value, float):
            if value != value:  # NaN
                return "NULL"
            if value == float('inf') or value == float('-inf'):
                return "NULL"
        return str(value)
    if isinstance(value, (datetime.date, datetime.datetime)):
        # Representa como literal de timestamp ISO
        return "'" + value.strftime('%Y-%m-%d %H:%M:%S') + "'"
    if isinstance(value, (bytes, bytearray, memoryview)):
        # Bytea em hex: '\xDEADBEEF'
        hexstr = bytes(value).hex()
        return "'\\x" + hexstr + "'::bytea"
    # String e outros
    s = str(value)
    s = s.replace("'", "''")
    return "'" + s + "'"


def generate_create_table(table: str, columns: List[Dict[str, Any]], pks: List[str]) -> str:
    """Gera o comando CREATE TABLE, transformando o primeiro campo em serial PRIMARY KEY."""
    if not columns:
        return f"-- AVISO: Tabela '{normalize_name(table)}' sem colunas, CREATE TABLE não gerado.\n"

    parts: List[str] = []
    
    # Processa o primeiro campo como a chave primária serial
    first_col = columns[0]
    first_col_name = quote_ident(first_col["name"])
    parts.append(f"    {first_col_name} serial NOT NULL PRIMARY KEY")

    # Processa os campos restantes
    for col in columns[1:]:
        colname = quote_ident(col["name"])
        pgtype = odbc_type_to_pg(col.get("type_name"), col.get("column_size"), col.get("decimal_digits"))
        # Regras especiais para tipos baseados no nome do campo
        col_name = col.get("name", "")
        if is_quantidade_field(col_name):
            pgtype = "numeric(10,2)"
        elif is_hora_field(col_name):
            pgtype = "time"
        
        # Não especifica NULL ou NOT NULL, usando o padrão do banco
        parts.append(f"    {colname} {pgtype}")

    cols_sql = ",\n".join(parts)
    return f"CREATE TABLE {quote_ident(table)} (\n{cols_sql}\n);\n"


def generate_inserts(cursor: pyodbc.Cursor, table: str, columns: List[Dict[str, Any]]) -> List[str]:
    """Gera os comandos INSERT, ignorando a primeira coluna (que se tornou serial)."""
    if len(columns) < 2:
        # Não há dados para inserir se só havia uma coluna (que virou serial)
        return []

    # Ignora a primeira coluna para o INSERT e para o SELECT
    cols_for_insert = columns[1:]
    original_col_names = [c["name"] for c in cols_for_insert]
    normalized_col_names_sql = ", ".join(quote_ident(n) for n in original_col_names)
    
    # Usar SELECT * é mais robusto contra palavras-chave do Access como 'COUNT'.
    # A lógica de pular a primeira coluna será feita ao processar o resultado.
    query = f"SELECT * FROM [{table}]"

    inserts: List[str] = []
    try:
        cursor.execute(query)
    except pyodbc.Error as e:
        raise RuntimeError(f"Falha ao ler dados da tabela {table}: {e}")

    row = cursor.fetchone()
    while row is not None:
        # Pula o primeiro valor da linha, pois a primeira coluna virou serial
        values_sql = ", ".join(format_value(value) for value in row[1:])
        inserts.append(f"INSERT INTO {quote_ident(table)} ({normalized_col_names_sql}) VALUES ({values_sql});")
        row = cursor.fetchone()
    return inserts


class ConverterApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Conversor MDB/ACCDB para PostgreSQL")
        self.root.geometry("700x500")

        self.input_path = tk.StringVar()
        self.output_path = tk.StringVar()

        # --- Widgets ---
        # Frame para seleção de arquivos
        file_frame = tk.Frame(root, padx=10, pady=10)
        file_frame.pack(fill=tk.X)

        tk.Label(file_frame, text="Arquivo de Entrada (MDB/ACCDB):").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.input_entry = tk.Entry(file_frame, textvariable=self.input_path, width=70)
        self.input_entry.grid(row=1, column=0, sticky=tk.EW)
        tk.Button(file_frame, text="Procurar...", command=self.select_input_file).grid(row=1, column=1, padx=5)

        tk.Label(file_frame, text="Arquivo de Saída (.sql):").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.output_entry = tk.Entry(file_frame, textvariable=self.output_path, width=70)
        self.output_entry.grid(row=3, column=0, sticky=tk.EW)
        tk.Button(file_frame, text="Salvar como...", command=self.select_output_file).grid(row=3, column=1, padx=5)

        file_frame.grid_columnconfigure(0, weight=1)

        # Botão de conversão
        self.convert_button = tk.Button(root, text="Iniciar Conversão", command=self.start_conversion, font=("Helvetica", 12, "bold"), bg="#4CAF50", fg="white")
        self.convert_button.pack(pady=10)

        # Log
        log_frame = tk.Frame(root, padx=10, pady=5)
        log_frame.pack(fill=tk.BOTH, expand=True)
        tk.Label(log_frame, text="Log da Conversão:").pack(anchor=tk.W)
        self.log_text = scrolledtext.ScrolledText(log_frame, wrap=tk.WORD, height=15)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        self.log_text.configure(state='disabled')

    def log(self, message):
        self.root.after(0, self._log_thread_safe, message)

    def _log_thread_safe(self, message):
        self.log_text.configure(state='normal')
        self.log_text.insert(tk.END, message + "\n")
        self.log_text.see(tk.END)
        self.log_text.configure(state='disabled')

    def select_input_file(self):
        path = filedialog.askopenfilename(
            title="Selecione o arquivo de banco de dados",
            filetypes=(("Bancos de Dados Access", "*.mdb *.accdb"), ("Todos os arquivos", "*.*"))
        )
        if path:
            self.input_path.set(path)
            # Sugere um nome de arquivo de saída baseado no de entrada
            base_name = os.path.splitext(os.path.basename(path))[0]
            self.output_path.set(os.path.join(os.path.dirname(path), f"{base_name}_postgres.sql"))

    def select_output_file(self):
        path = filedialog.asksaveasfilename(
            title="Salvar arquivo SQL como...",
            defaultextension=".sql",
            filetypes=(("Arquivos SQL", "*.sql"), ("Todos os arquivos", "*.*"))
        )
        if path:
            self.output_path.set(path)

    def start_conversion(self):
        input_file = self.input_path.get()
        output_file = self.output_path.get()

        if not input_file or not output_file:
            messagebox.showerror("Erro", "Por favor, selecione os arquivos de entrada e saída.")
            return

        self.convert_button.config(state=tk.DISABLED, text="Convertendo...")
        self.log_text.configure(state='normal')
        self.log_text.delete('1.0', tk.END)
        self.log_text.configure(state='disabled')

        # Executa a conversão em uma thread para não travar a GUI
        thread = threading.Thread(target=self.run_conversion, args=(input_file, output_file))
        thread.start()

    def run_conversion(self, db_path, output_path):
        try:
            self.log(f"Iniciando conversão de '{os.path.basename(db_path)}'...")
            conn = connect_mdb(db_path)
            cur = conn.cursor()

            self.log("Buscando tabelas...")
            tables = get_tables(cur)
            if not tables:
                self.log("Nenhuma tabela de usuário encontrada no MDB.")
                return
            self.log(f"Tabelas encontradas: {', '.join(tables)}")

            statements: List[str] = []
            statements.append("-- SQL gerado a partir do Access para PostgreSQL")
            statements.append(f"-- Fonte: {db_path}")
            statements.append(f"-- Gerado em: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            statements.append("SET standard_conforming_strings = on;")

            for t in tables:
                self.log(f"Processando schema da tabela '{t}'...")
                cols = get_columns(cur, t)
                if not cols:
                    self.log(f"Aviso: tabela '{t}' sem colunas detectadas (pode ser vinculada).")
                    continue
                pks = get_primary_keys(cur, t)
                statements.append(generate_create_table(t, cols, pks))

            statements.append("\n-- ================= DATA =================")

            for t in tables:
                self.log(f"Exportando dados da tabela '{t}'...")
                cols = get_columns(cur, t)
                if not cols:
                    continue
                inserts = generate_inserts(cur, t, cols)
                if inserts:
                    statements.append(f"\n-- Tabela: {quote_ident(t)}")
                    statements.extend(inserts)
                    self.log(f" -> {len(inserts)} registros exportados.")

            sql_text = "\n".join(statements) + "\n"

            with open(output_path, "w", encoding="utf-8") as f:
                f.write(sql_text)

            self.log("\nConversão concluída com sucesso!")
            self.log(f"Arquivo SQL gerado em: {os.path.abspath(output_path)}")
            messagebox.showinfo("Sucesso", f"Conversão concluída!\nArquivo salvo em:\n{output_path}")

        except Exception as e:
            self.log(f"ERRO: {e}")
            messagebox.showerror("Erro na Conversão", f"Ocorreu um erro:\n{e}")
        finally:
            self.root.after(0, self._conversion_done)

    def _conversion_done(self):
        self.convert_button.config(state=tk.NORMAL, text="Iniciar Conversão")

if __name__ == "__main__":
    root = tk.Tk()
    app = ConverterApp(root)
    root.mainloop()
