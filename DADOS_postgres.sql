-- SQL gerado a partir do Access para PostgreSQL
-- Fonte: C:/Users/<USER>/Documents/Sistemas/mdb to postgresql/DADOS.MDB
-- Gerado em: 2025-08-13 15:48:57
SET standard_conforming_strings = on;
CREATE TABLE agenda (
    codigo_agenda serial NOT NULL PRIMARY KEY,
    nome_agenda varchar(100),
    mes_agenda varchar(100)
);

CREATE TABLE anotacoes (
    codigo_anotacoes serial NOT NULL PRIMARY KEY,
    data_anotacoes date,
    nome_anotacoes varchar(100),
    descricao_anotacoes text
);

CREATE TABLE busca (
    n serial NOT NULL PRIMARY KEY
);

CREATE TABLE cadastro_aluno (
    codigo_aluno serial NOT NULL PRIMARY KEY,
    aluno varchar(100),
    endereco_aluno varchar(100),
    bairro_aluno varchar(100),
    cidade_aluno varchar(100),
    estado_aluno varchar(100),
    cep_aluno varchar(100),
    telefone_1_aluno varchar(100),
    telefone_2_aluno varchar(100),
    celular_aluno varchar(100),
    cpf_aluno varchar(100),
    rg_aluno varchar(100),
    sexo_aluno varchar(100),
    idade_aluno varchar(100),
    data_nascimento_aluno date,
    mes_nascimento_aluno numeric(10),
    email_aluno varchar(100),
    anotacoes_aluno text,
    foto_1_aluno bytea,
    foto_2_aluno bytea,
    foto_3_aluno bytea
);

CREATE TABLE cadastro_departamento (
    codigo_departamento serial NOT NULL PRIMARY KEY,
    departamento varchar(100)
);

CREATE TABLE cadastro_fornecedores (
    codigo_fornecedor serial NOT NULL PRIMARY KEY,
    razao_social_fornecedor varchar(100),
    nome_fantasia_fornecedor varchar(100),
    endereco_fornecedor varchar(100),
    bairro_fornecedor varchar(100),
    cidade_fornecedor varchar(100),
    estado_fornecedor varchar(100),
    cep_fornecedor varchar(100),
    telefone_1_fornecedor varchar(100),
    telefone_2_fornecedor varchar(100),
    fax_fornecedor varchar(100),
    email_fornecedor varchar(100),
    site_fornecedor varchar(100),
    anotacoes_fornecedor text,
    foto_1_fornecedor bytea,
    foto_2_fornecedor bytea,
    foto_3_fornecedor bytea
);

CREATE TABLE cadastro_funcionarios (
    codigo_funcionario serial NOT NULL PRIMARY KEY,
    funcionario varchar(100),
    endereco_funcionario varchar(100),
    bairro_funcionario varchar(100),
    cidade_funcionario varchar(100),
    estado_funcionario varchar(100),
    cep_funcionario varchar(100),
    telefone_1_funcionario varchar(100),
    telefone_2_funcionario varchar(100),
    celular_funcionario varchar(100),
    email_funcionario varchar(100),
    cargo_funcionario varchar(100),
    setor_funcionario varchar(100),
    cpf_funcionario varchar(100),
    rg_funcionario varchar(100),
    data_admissao_funcionario date,
    salario_funcionario numeric(10,2),
    status_funcionario varchar(100),
    anotacoes_funcionario text,
    foto_1_funcionario bytea,
    foto_2_funcionario bytea,
    foto_3_funcionario bytea
);

CREATE TABLE cadastro_instrutores (
    codigo_instrutor serial NOT NULL PRIMARY KEY,
    instrutor varchar(100),
    endereco_instrutor varchar(100),
    bairro_instrutor varchar(100),
    cidade_instrutor varchar(100),
    estado_instrutor varchar(100),
    cep_instrutor varchar(100),
    telefone_1_instrutor varchar(100),
    telefone_2_instrutor varchar(100),
    celular_instrutor varchar(100),
    email_instrutor varchar(100),
    cpf_instrutor varchar(100),
    rg_instrutor varchar(100),
    nascimento_instrutor date,
    mes_nascimento numeric(10),
    instrutor_de_instrutor varchar(100),
    anotacoes_instrutor text,
    foto_1_instrutor bytea,
    foto_2_instrutor bytea,
    foto_3_instrutor bytea
);

CREATE TABLE cadastro_produtos (
    codigo_produto serial NOT NULL PRIMARY KEY,
    produto varchar(100),
    preco_produto numeric(10,2),
    preco_custo_produto numeric(10,2),
    visualizar_custo_produto numeric(10),
    anotacoes_produto text,
    foto_1_produto bytea,
    foto_2_produto bytea,
    foto_3_produto bytea,
    estoque_produto integer
);

CREATE TABLE cadastro_transportadoras (
    codigo_transportadora serial NOT NULL PRIMARY KEY,
    razao_social_transportadora varchar(100),
    nome_fantasia_transportadora varchar(100),
    endereco_transportadora varchar(100),
    bairro_transportadora varchar(100),
    cidade_transportadora varchar(100),
    estado_transportadora varchar(100),
    cep_transportadora varchar(100),
    telefone_1_transportadora varchar(100),
    telefone_2_transportadora varchar(100),
    email_transportadora varchar(100),
    site_transportadora varchar(100),
    contato_transportadora varchar(100),
    anotacoes_transportadora text,
    foto_1_transportadora bytea,
    foto_2_transportadora bytea,
    foto_3_transportadora bytea
);

CREATE TABLE cadastro_vendedor (
    codigo_vendedor serial NOT NULL PRIMARY KEY,
    vendedor varchar(100),
    endereco_vendedor varchar(100),
    bairro_vendedor varchar(100),
    cidade_vendedor varchar(100),
    estado_vendedor varchar(100),
    cep_vendedor varchar(100),
    telefone_1_vendedor varchar(100),
    telefone_2_vendedor varchar(100),
    celular_vendedor varchar(100),
    email_vendedor varchar(100),
    setor_vendedor varchar(100),
    cargo_vendedor varchar(100),
    comissao_%_vendedor numeric(10),
    anotacoes_vendedor text,
    foto_1_vendedor bytea,
    foto_2_vendedor bytea,
    foto_3_vendedor bytea
);

CREATE TABLE cadastro_licenca (
    nome serial NOT NULL PRIMARY KEY,
    tipo varchar(100),
    cnpj varchar(100),
    cpf varchar(100),
    data date,
    id varchar(100)
);

CREATE TABLE cobranca (
    codigo_cobranca serial NOT NULL PRIMARY KEY,
    data_cobranca date,
    codigo_cliente_cobranca integer
);

CREATE TABLE codigo (
    codigo serial NOT NULL PRIMARY KEY
);

CREATE TABLE compras (
    codigo_compras serial NOT NULL PRIMARY KEY,
    data_compra date,
    status_compra varchar(100),
    total_compra numeric(10,2)
);

CREATE TABLE contatos (
    codigo_contatos serial NOT NULL PRIMARY KEY
);

CREATE TABLE ficha (
    codigo_ficha serial NOT NULL PRIMARY KEY,
    data_ficha date,
    codigo_aluno_ficha integer,
    codigo_instrutor_ficha integer,
    complemento_ficha text,
    pratica_atividade_ficha varchar(100),
    obs_ativ_fisica_ficha varchar(100),
    usa_medicamentos_ficha varchar(100),
    obs_medicamentos_ficha varchar(100),
    problemas_cardiacos_ficha varchar(100),
    obs_prob_cardiacos_ficha varchar(100),
    problemas_hormonais_ficha varchar(100),
    obs_hormonais_ficha varchar(100),
    problemas_intestino_ficha varchar(100),
    obs_intestino_ficha varchar(100),
    problemas_articulares_ficha varchar(100),
    obs_articulares_ficha varchar(100),
    problemas_musculares_ficha varchar(100),
    obs_musculares_ficha varchar(100),
    problemas_respiratorios_ficha varchar(100),
    obs_respiratorios_ficha varchar(100),
    problemas_alergicos_ficha varchar(100),
    obs_alergicos_ficha varchar(100),
    problemas_osseos_ficha varchar(100),
    obs_osseos_ficha varchar(100),
    fuma_ficha? varchar(100),
    obs_fuma_ficha varchar(100),
    tem_lesao_ficha varchar(100),
    obs_lesao_ficha varchar(100),
    pressao_ficha varchar(100),
    condicao_pressao_ficha varchar(100),
    complemento_informativo_ficha text,
    indicado_alimentacao varchar(100),
    dias_horarios_exercicios_ficha time,
    por_aluno varchar(100),
    mes_ficha varchar(100),
    anotacoes_ficha text
);

CREATE TABLE financeiro (
    codigo_financeiro serial NOT NULL PRIMARY KEY,
    data_inicial_caixa_financeiro date,
    data_final_caixa_financeiro date,
    saldo_caixa_financeiro numeric(10,2),
    anotacoes_funcion_financeiro text,
    anotacoes_financeiro text,
    mostrar_cheques_recebidos_fina varchar(100),
    procurar_cheque_recebido_finan varchar(100),
    procurar_cheque_emitido_finan varchar(100),
    data_inic_entradas_caixa_finan date,
    data_fin_entradas_caixa_finan date,
    data_inic_saidas_caixa_finan date,
    data_fin_saidas_caixa_finan date
);

CREATE TABLE frequencia (
    codigo_frequencia serial NOT NULL PRIMARY KEY,
    data_frequencia date,
    anotacoes_frequencia text
);

CREATE TABLE grid_agenda (
    codigo_agenda serial NOT NULL PRIMARY KEY,
    assunto_agenda varchar(100),
    para_o_dia_:_agenda date,
    status_agenda varchar(100)
);

CREATE TABLE grid_alimentacao (
    codigo_ficha serial NOT NULL PRIMARY KEY,
    itens_alimentacao varchar(100),
    beneficios_alimentacao varchar(100)
);

CREATE TABLE grid_avaliacao (
    codigo_ficha serial NOT NULL PRIMARY KEY,
    data_avaliacao date,
    teste_aplicado_avaliacao varchar(100),
    numero_repeticoes_avaliacao varchar(100),
    resultado_avaliacao varchar(100)
);

CREATE TABLE grid_caixa (
    codigo_financeiro serial NOT NULL PRIMARY KEY,
    data_caixa_gri date,
    descricao_caixa_grid varchar(100),
    entrada_caixa_gri numeric(10,2),
    saida_caixa_gri numeric(10,2),
    cod~lan varchar(100)
);

CREATE TABLE grid_compras (
    codigo_compras serial NOT NULL PRIMARY KEY,
    descricao_compra varchar(100),
    quantidade_compra numeric(10,2),
    valor_unitario_compra numeric(10,2),
    total_itens_compra numeric(10,2)
);

CREATE TABLE grid_contas_pagar (
    codigo_financeiro serial NOT NULL PRIMARY KEY,
    descricao_conta_pagar_grid varchar(100),
    vencimento_conta_pagar_grid date,
    valor_conta_pagar_grid numeric(10,2),
    status_conta_pagar_grid varchar(100),
    observacao_conta_pagar_grid varchar(100),
    int~lan numeric(10,2)
);

CREATE TABLE grid_contas_receber (
    codigo_financeiro serial NOT NULL PRIMARY KEY,
    descricao_conta_receber_g varchar(100),
    vencimento_conta_receber_g date,
    valor_conta_receber_g numeric(10,2),
    status_conta_receber_g varchar(100),
    enviar_no_caixa_conta_receber varchar(100),
    int~lan numeric(10,2)
);

CREATE TABLE grid_contatos (
    codigo_contatos serial NOT NULL PRIMARY KEY,
    nome_contato varchar(100),
    telefone_contato varchar(100),
    celulares_contato varchar(100),
    email_contato varchar(100),
    site_contato varchar(100),
    complemento_contato varchar(100)
);

CREATE TABLE grid_despesas (
    codigo_financeiro serial NOT NULL PRIMARY KEY,
    descricao_despesa_grid varchar(100),
    valor_despesa_grid numeric(10,2),
    data_despesa_grid date,
    int~lan numeric(10,2)
);

CREATE TABLE grid_frequencia (
    codigo_frequencia serial NOT NULL PRIMARY KEY,
    codigo_aluno_frequencia_grid integer,
    hora_frequencia_grid time,
    observacao_frequencia_grid varchar(100)
);

CREATE TABLE grid_medidas (
    codigo_ficha serial NOT NULL PRIMARY KEY,
    mes_medidas varchar(100),
    data_medidas date,
    peso_medidas numeric(10),
    altura_medidas numeric(10),
    biceps_direito_medidas numeric(10),
    biceps_esquerdo_medidas numeric(10),
    coxa_direita_medidas numeric(10),
    coxa_esquerda_medidas numeric(10),
    ante_braco_direito_medidas numeric(10),
    ante_braco_esquerdo_medidas numeric(10),
    panturrilha_direita_medidas numeric(10),
    panturrilha_esquerda_medidas numeric(10),
    peitoral_medidas numeric(10),
    quadril_medidas numeric(10),
    cintura_medidas numeric(10)
);

CREATE TABLE grid_mensalidade (
    codigo_ficha serial NOT NULL PRIMARY KEY,
    mes_mensalidade varchar(100),
    vencimento_mensalidade date,
    valor_mensalidade numeric(10,2),
    multa_mesalidade numeric(10,2),
    valor_pagar_mensalidade numeric(10,2),
    situacao_mensalidade varchar(100),
    cod_financeiro numeric(10),
    int~lan numeric(10,2)
);

CREATE TABLE grid_modalidade (
    codigo_ficha serial NOT NULL PRIMARY KEY,
    codigo_modalidade integer,
    valor_modalidade numeric(10,2),
    desconto_modalidade numeric(10,2),
    sub_total_modalidade numeric(10,2)
);

CREATE TABLE grid_pagamentos (
    codigo_financeiro serial NOT NULL PRIMARY KEY,
    cod_funcionario_pagamento_grid integer,
    descricao_pagamento_grid varchar(100),
    valor_pagamento_grid numeric(10,2),
    data_pagamento_grid date,
    status_pagamento_grid varchar(100),
    int~lan numeric(10,2)
);

CREATE TABLE grid_produtos (
    codigo_ficha serial NOT NULL PRIMARY KEY,
    codigo_produto_grid integer,
    quantidade_produto numeric(10,2),
    preco_produto_grid numeric(10,2),
    total_itens_produto_grid numeric(10,2),
    situacao_produto_grid varchar(100),
    cod_financeiro numeric(10),
    data_produto_grid date,
    int~lan numeric(10,2)
);

CREATE TABLE help_desk (
    numero_atendimento_help_desk serial NOT NULL PRIMARY KEY,
    data_help_desk date,
    hora_help_desk time,
    previsao_para_solucao date,
    tipo_solicitante_help_desk varchar(100),
    solicitante_help_desk varchar(100),
    email_help_desk varchar(100),
    msn_help_desk varchar(100),
    telefone_help_desk varchar(100),
    codigo_departamento_help_desk integer,
    situacao_help_desk varchar(100),
    prioridade_help_desk varchar(100),
    solucionado_dia date,
    informado_da_solucao_help_desk varchar(100),
    solicitacao_help_desk text,
    solucao_help_desk text,
    mes_help_desk varchar(100)
);

CREATE TABLE licenca (
    com_logotipo_?_licenca serial NOT NULL PRIMARY KEY,
    logotipo_licenca bytea,
    nome_licenciado_licenca varchar(100),
    cpf_cnpj_licenciado_licenca varchar(100),
    telefones_licenca varchar(100),
    endereco_licenca varchar(100),
    cidade_licenca varchar(100)
);

CREATE TABLE modalidade (
    codigo_modalidade serial NOT NULL PRIMARY KEY,
    nome_modalidade varchar(100),
    valor_modalidade numeric(10,2),
    anotacoes_modalidade text,
    imagem_1_modalidade bytea,
    imagem_2_modalidade bytea,
    imagem_3_modalidade bytea
);

CREATE TABLE promissoria (
    numero_promissoria serial NOT NULL PRIMARY KEY,
    vencimento_promissoria date,
    valor_promissoria numeric(10,2),
    ao_promissoria varchar(100),
    a_promissoria varchar(100),
    cpf_promissosria varchar(100),
    a_quantia_de_promissoria varchar(100),
    pagavel_na_cidade_: varchar(100),
    estado_promissoria varchar(100),
    emitente_promissoria varchar(100),
    cpf_emitente_promissoria varchar(100),
    endereco_emitente_promissoria varchar(100),
    data_promissoria date
);

CREATE TABLE pw~grupos (
    nome serial NOT NULL PRIMARY KEY
);

CREATE TABLE pw~tabelas (
    grupo serial NOT NULL PRIMARY KEY,
    bd varchar(100),
    nome varchar(100),
    permissoes varchar(100)
);

CREATE TABLE pw~usuarios (
    grupo serial NOT NULL PRIMARY KEY,
    nome varchar(100),
    senha varchar(100),
    obs varchar(100)
);

CREATE TABLE recibo (
    numero_recibo serial NOT NULL PRIMARY KEY,
    data_recibo date,
    nossa_empresa_recibo varchar(100),
    recebemos_de_recibo varchar(100),
    endereco_recibo varchar(100),
    cidade_recibo varchar(100),
    estado_recibo varchar(100),
    o_valor_de_recibo numeric(10,2),
    referente_a_recibo varchar(100)
);

CREATE TABLE sys~sequencial (
    bd serial NOT NULL PRIMARY KEY,
    tabela varchar(100),
    campo varchar(100),
    chave varchar(100),
    valor varchar(100),
    valor_anterior varchar(100),
    estacao varchar(100),
    identificacao varchar(100)
);


-- ================= DATA =================

-- Tabela: cadastro_licenca
INSERT INTO cadastro_licenca (tipo, cnpj, cpf, data, id) VALUES ('', '', '', NULL, '');

-- Tabela: licenca
INSERT INTO licenca (logotipo_licenca, nome_licenciado_licenca, cpf_cnpj_licenciado_licenca, telefones_licenca, endereco_licenca, cidade_licenca) VALUES (NULL, '', '', '', '', '');

-- Tabela: pw~tabelas
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÁÄÁÓÔÒÏáÄÅÐÁŸÔÁÍÅÎÔÏæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÁÄÁÓÔÒÏáÆÏÒÎÅÃÅÄÏÒÅÓæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÁÄÁÓÔÒÏáÆÕÎÃÉÏÎAÒÉÏÓæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÁÄÁÓÔÒÏáÁÌÕÎÏâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÁÄÁÓÔÒÏáÐÒÏÄÕÔÏÓèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÁÄÁÓÔÒÏáÉÎÓÔŸÕÔÏÒÅÓîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÁÄÁÓÔÒÏáÖÅÎÄÅÄÏÒèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÁÄÁÓÔÒÏáÔÒÁÎÓÐÏÒÔÁÄÏŸÁÓê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÁÄÁÓÔÒÏßÌÉÃÅÎÃÁêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÁÇÅÎÄÁâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÆÉÃÈÁæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÆÉÎÁÎÃÅÉÒÏêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÆÒÅÑÕJÎÃÉÁêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÏÄÉÇÏâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÏÂÒÁÎGÁêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÏÍÐÒÁÓêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÁÎÏÔÁGUÅÓèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÃÏÎÔÁÔÏÓêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÂÕÓÃÁæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÃÁÉØÁêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÄÅÓÐÅÓÁÓæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÁÇÅÎÄÁïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÆÒÅÑÕJÎÃÉÁêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÁÌÉÍÅÎÔÁGCÏêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÃÏÍÐÒÁÓîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÃÏÎÔÁÓäÐÁÇÁÒèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÃÏÎÔÁÓäÒÅÃÅÂÅÒïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÃÏÎÔÁÔÏÓæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÁŸÁÌÉÁGCÏâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÐÁÇÁÍÅÎÔÏÓêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÐÒÏÄÕÔÏÓæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÍÅÄÉÄÁÓîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÍÅÎÓÁÌÉÄÁÄÅêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÇÒÉÄåÍÏÄÁÌÉÄÁÄÅêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÐÒÏÍÉÓÓSÒÉÁïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÈÅÌÐåÄÅÓËèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÌÉÃÅÎÃÁêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÍÏÄÁÌÉÄÁÄÅêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÄÁÄÏÓãÍÄÂèêïîæâêêèêïîæâêê', 'ÒÅÃÉÂÏâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííò÷öæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõ÷óæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõ÷üæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõ÷ýæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòðòæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòðõæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõðõæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòðüæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòñöæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííôñüæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóó÷æâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòò÷æâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõô÷æâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõö÷æâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõõ÷æâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóóðæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóòðæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòòðæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòôðæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõóðæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõôðæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõõðæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóóñæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóòñæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòòñæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòôñæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõóñæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõöñæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõõñæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóóòæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóóôæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóóöæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóóõæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóòóæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóòòæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóòöæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòòóæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòòòæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòòöæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòôóæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòôôæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòôöæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòôõæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòöòæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòõöæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííôòõæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííôöôæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííôöõæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõóóæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõôóæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõôòæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõôôæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõôöæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõôõæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõöôæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõööæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõöõæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõõóæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõõòæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõõöæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóóüæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòòüæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííôõüæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõóüæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõôüæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõõüæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóóýæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóòýæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòòýæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõôýæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõõýæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóü÷æâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõü÷æâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóüðæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóüñæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõüñæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóüòæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóüôæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóüöæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóüõæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòüòæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõüôæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõüöæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõüõæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóüüæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóüýæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòüýæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóý÷æâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòý÷æâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóýðæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõýðæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóýñæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòýñæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõýñæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóýóæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóýòæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóýôæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóýöæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóýõæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòýóæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòýõæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííõýóæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóýüæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííóýýæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('êèêïîæâêêèêïîæâêêèêïîæâêê', 'Ííòýýæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');
INSERT INTO pw~tabelas (bd, nome, permissoes) VALUES ('ÎíÍÄÂæâêêèêïîæâêêèêïîæâêê', 'Îèêïîæâêêèêïîæâêêèêïîæâêê', 'ðòðõîæâêêèêïîæâêêèêïîæâêê');

-- Tabela: pw~usuarios
INSERT INTO pw~usuarios (nome, senha, obs) VALUES ('ËÁÄïîæâêêèêïîæâêêèêïîæâêê', 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê', 'êèêïîæâêêèêïîæâêêèêïîæâêê');
